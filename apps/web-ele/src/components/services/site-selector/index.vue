<script lang="ts" setup>
import type { SiteSelectorProps, SiteSelectorEmits, SiteInfo } from './types';

import { ref, computed } from 'vue';
import { ElInput } from 'element-plus';
import SiteModal from './components/SiteModal.vue';

defineOptions({
  name: 'SiteSelector',
});

const props = withDefaults(defineProps<SiteSelectorProps>(), {
  placeholder: '请选择站点',
  modelValue: '',
  disabled: false,
  inputClass: '',
});

const emit = defineEmits<SiteSelectorEmits>();

// Modal显示状态
const modalVisible = ref(false);

// 当前选中的站点信息
const selectedSite = ref<SiteInfo | null>(null);

// 输入框显示值
const displayValue = computed(() => {
  return selectedSite.value?.name || props.modelValue || '';
});

// 输入框聚焦事件
const handleInputFocus = () => {
  if (props.disabled) return;
  modalVisible.value = true;
};

// 输入框点击事件
const handleInputClick = () => {
  if (props.disabled) return;
  modalVisible.value = true;
};

// Modal确认事件
const handleModalConfirm = (site: SiteInfo) => {
  selectedSite.value = site;
  modalVisible.value = false;

  // 更新v-model值
  emit('update:modelValue', site.name);

  // 触发站点选择事件
  emit('site-selected', site);
};

// Modal取消事件
const handleModalCancel = () => {
  modalVisible.value = false;
};

// 清空选择
const handleClear = () => {
  selectedSite.value = null;
  emit('update:modelValue', '');
};

// 暴露方法给父组件
defineExpose({
  /** 获取当前选中的站点信息 */
  getSelectedSite: () => selectedSite.value,
  /** 清空选择 */
  clear: handleClear,
  /** 打开选择器 */
  open: () => {
    if (!props.disabled) {
      modalVisible.value = true;
    }
  },
});
</script>

<template>
  <div class="site-selector">
    <!-- 输入框 -->
    <ElInput
      :model-value="displayValue"
      :placeholder="placeholder"
      :disabled="disabled"
      :class="inputClass"
      readonly
      clearable
      @focus="handleInputFocus"
      @click="handleInputClick"
      @clear="handleClear"
    >
      <template #suffix>
        <div class="flex items-center">
          <svg
            class="w-4 h-4 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M19 9l-7 7-7-7"
            />
          </svg>
        </div>
      </template>
    </ElInput>

    <!-- 站点选择Modal -->
    <SiteModal
      v-model:visible="modalVisible"
      :selected-site="selectedSite || undefined"
      @confirm="handleModalConfirm"
      @cancel="handleModalCancel"
    />
  </div>
</template>

<style scoped>
.site-selector {
  width: 100%;
}

:deep(.el-input__wrapper) {
  cursor: pointer;
}

:deep(.el-input__inner) {
  cursor: pointer;
}
</style>
