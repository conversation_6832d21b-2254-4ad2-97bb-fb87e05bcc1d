// 站点选择器相关类型定义

export interface SiteInfo {
  id: string;
  name: string;
  enterprise: string;
  region: string;
  address: string;
  type: string;
}

export interface EnterpriseTreeNode {
  id: string;
  label: string;
  value: string;
  children?: EnterpriseTreeNode[];
}

export interface RegionTreeNode {
  id: string;
  label: string;
  value: string;
  children?: RegionTreeNode[];
}

export interface SiteSearchForm {
  siteType: string;
  keyword: string;
}

export interface SiteSelectorProps {
  /** 输入框占位符 */
  placeholder?: string;
  /** 输入框值 */
  modelValue?: string;
  /** 是否禁用 */
  disabled?: boolean;
  /** 输入框样式类 */
  inputClass?: string;
}

export interface SiteSelectorEmits {
  'update:modelValue': [value: string];
  'site-selected': [site: SiteInfo];
}

export interface SiteModalProps {
  /** 是否显示Modal */
  visible: boolean;
  /** 当前选中的站点 */
  selectedSite?: SiteInfo;
}

export interface SiteModalEmits {
  'update:visible': [visible: boolean];
  'confirm': [site: SiteInfo];
  'cancel': [];
}
