# 站点选择器组件 (SiteSelector)

基于 VbenModal 封装的站点选择组件，通过 ElInput 聚焦触发，提供企业和区域分类的站点选择功能。

## 功能特性

- 🎯 **输入框触发**：点击或聚焦输入框自动打开选择器
- 🏢 **企业分类**：支持按企业维度筛选站点
- 🌍 **区域分类**：支持按省市区域维度筛选站点  
- 🔍 **智能搜索**：左侧树形控件和右侧表格都支持搜索
- 📊 **表格展示**：VXE表格展示站点详细信息
- ✅ **单选模式**：树形控件单选，表格行选择
- 🎨 **响应式设计**：适配不同屏幕尺寸

## 组件结构

```
site-selector/
├── index.vue              # 主组件入口
├── types.ts               # TypeScript 类型定义
├── data.ts                # 数据配置和 Mock 数据
├── components/
│   └── SiteModal.vue      # Modal 内容组件
├── example.vue            # 使用示例
└── README.md              # 说明文档
```

## 基础使用

```vue
<template>
  <SiteSelector
    v-model="selectedSite"
    placeholder="请选择站点"
    @site-selected="handleSiteSelected"
  />
</template>

<script setup>
import { ref } from 'vue';
import SiteSelector from '@/components/services/site-selector/index.vue';

const selectedSite = ref('');

const handleSiteSelected = (site) => {
  console.log('选中的站点:', site);
};
</script>
```

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `modelValue` | `string` | `''` | 输入框显示值，支持 v-model |
| `placeholder` | `string` | `'请选择站点'` | 输入框占位符 |
| `disabled` | `boolean` | `false` | 是否禁用 |
| `inputClass` | `string` | `''` | 输入框自定义样式类 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:modelValue` | `(value: string)` | 输入框值变化时触发 |
| `site-selected` | `(site: SiteInfo)` | 选择站点时触发 |

## 方法

通过 ref 可以调用以下方法：

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| `getSelectedSite()` | - | `SiteInfo \| null` | 获取当前选中的站点信息 |
| `clear()` | - | `void` | 清空当前选择 |
| `open()` | - | `void` | 程序化打开选择器 |

## 数据类型

### SiteInfo

```typescript
interface SiteInfo {
  id: string;        // 站点ID
  name: string;      // 站点名称
  enterprise: string; // 所属企业
  region: string;    // 所在区域
  address: string;   // 详细地址
  type: string;      // 站点类型
}
```

## Modal 内部功能

### 左侧面板
- **企业标签页**：
  - 企业树形结构展示
  - 支持企业名称搜索
  - 单选模式，选中后过滤右侧表格

- **区域标签页**：
  - 省市区域树形结构
  - 支持区域名称搜索  
  - 单选模式，选中后过滤右侧表格

### 右侧面板
- **搜索表单**：
  - 站点类型下拉选择（变电站、发电厂、配电房、充电站）
  - 搜索关键字输入框
  
- **VXE表格**：
  - 站点名称、企业、区域、用户地址四列
  - 支持分页、排序
  - 行点击选中，高亮显示
  - 响应式列宽

## 样式定制

组件使用 Tailwind CSS 和设计系统变量，支持主题切换：

```vue
<style scoped>
/* 自定义输入框样式 */
:deep(.el-input__wrapper) {
  cursor: pointer;
}

/* 自定义表格选中行样式 */
:deep(.vxe-table .vxe-body--row.row--current) {
  background-color: hsl(var(--primary) / 0.1);
}
</style>
```

## 扩展开发

### 添加新的站点类型

在 `data.ts` 中修改 `siteTypeOptions`：

```typescript
export const siteTypeOptions = [
  { label: '全部', value: '' },
  { label: '变电站', value: 'substation' },
  { label: '发电厂', value: 'power_plant' },
  // 添加新类型
  { label: '储能站', value: 'energy_storage' },
];
```

### 自定义表格列

修改 `useSiteTableColumns` 函数：

```typescript
export function useSiteTableColumns() {
  return [
    // 现有列...
    {
      align: 'center',
      field: 'status',
      title: '状态',
      minWidth: 80,
      cellRender: { name: 'CellTag' },
    },
  ];
}
```

### 接入真实API

替换 `SiteModal.vue` 中的 Mock 数据查询：

```typescript
// 替换 mockSiteData 为真实API调用
const result = await getSiteListApi({
  siteType: formValues?.siteType,
  keyword: formValues?.keyword,
  enterprise: selectedEnterpriseNode.value,
  region: selectedRegionNode.value,
});
```

## 测试页面

组件已创建完成，可以通过以下方式测试：

1. **演示页面**：`apps/web-ele/src/views/examples/site-selector-demo.vue`
2. **组件示例**：`apps/web-ele/src/components/services/site-selector/example.vue`

## 已实现功能

✅ **基础功能**
- ElInput 聚焦触发 Modal 显示
- VbenModal 封装，支持确定/取消操作
- 左右两列布局设计

✅ **左侧面板**
- ElTabs 切换企业/区域视图
- 企业树形控件，支持搜索和单选
- 区域树形控件（省市层级），支持搜索和单选
- 树节点选择自动过滤右侧表格

✅ **右侧面板**
- VXE 表格展示站点数据
- 搜索表单（站点类型下拉 + 关键字输入）
- 表格四列：站点名称、企业、区域、用户地址
- 行点击选择，分页支持

✅ **交互功能**
- 输入框显示选中站点名称
- 清空按钮快速清除选择
- 组件方法暴露（获取选中站点、清空、程序化打开）
- 事件回调（站点选择事件）

## 注意事项

1. 组件依赖 VbenModal、Element Plus 和 VXE Table
2. 需要在项目中正确配置 Tailwind CSS
3. Mock 数据仅用于演示，生产环境需要接入真实API
4. 树形控件默认单选模式，如需多选请修改相关配置
5. 已修复 TypeScript 类型检查问题
