import type { VbenFormSchema } from '@vben/common-ui';

import type { EnterpriseTreeNode, RegionTreeNode, SiteInfo } from './types';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { h } from 'vue';
import { IconifyIcon } from '@vben-core/icons';



// 站点类型选项
export const siteTypeOptions = [
  { label: '全部', value: '' },
  { label: '变电站', value: 'substation' },
  { label: '发电厂', value: 'power_plant' },
  { label: '配电房', value: 'distribution_room' },
  { label: '充电站', value: 'charging_station' },
];

// 搜索表单配置
export function useSiteSearchFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Select',
      componentProps: {
        class: 'w-full h-[40px]',
        placeholder: '请选择站点类型',
        options: siteTypeOptions,
      },
      fieldName: 'siteType',
      label: '站点类型',
    },
    {
      component: 'Input',
      componentProps: {
        class: 'w-full h-[40px]',
        placeholder: '请输入搜索关键字',
        'suffix-icon': () => h(IconifyIcon, { icon: 'lucide:search', class: 'w-4 h-4 text-gray-400' }),
      },
      fieldName: 'keyword',
      label: '搜索关键字',
    },
  ];
}

// 表格列配置
export function useSiteTableColumns(): VxeTableGridOptions<SiteInfo>['columns'] {
  return [
    {
      align: 'center',
      field: 'name',
      title: '站点名称',
      minWidth: 150,
    },
    {
      align: 'center',
      field: 'enterprise',
      title: '企业',
      minWidth: 120,
    },
    {
      align: 'center',
      field: 'region',
      title: '区域',
      minWidth: 100,
    },
    {
      align: 'center',
      field: 'address',
      title: '用户地址',
      minWidth: 200,
    },
  ];
}

// Mock企业树形数据
export const mockEnterpriseTreeData: EnterpriseTreeNode[] = [
  {
    id: 'enterprise_1',
    label: '微电网研究院',
    value: 'enterprise_1',
  },
  {
    id: 'enterprise_2',
    label: '新能源科技公司',
    value: 'enterprise_2',
  },
  {
    id: 'enterprise_3',
    label: '智慧电力集团',
    value: 'enterprise_3',
  },
  {
    id: 'enterprise_4',
    label: '绿色能源有限公司',
    value: 'enterprise_4',
  },
];

// Mock区域树形数据
export const mockRegionTreeData: RegionTreeNode[] = [
  {
    id: 'region_all',
    label: '全部',
    value: 'all',
    children: [
      {
        id: 'beijing',
        label: '北京市',
        value: 'beijing',
        children: [
          {
            id: 'beijing_chaoyang',
            label: '朝阳区',
            value: 'beijing_chaoyang',
          },
          {
            id: 'beijing_haidian',
            label: '海淀区',
            value: 'beijing_haidian',
          },
        ],
      },
      {
        id: 'hebei',
        label: '河北省',
        value: 'hebei',
        children: [
          {
            id: 'shijiazhuang',
            label: '石家庄市',
            value: 'shijiazhuang',
          },
          {
            id: 'tangshan',
            label: '唐山市',
            value: 'tangshan',
          },
        ],
      },
    ],
  },
];

// Mock站点数据 - 扩展数据以支持企业和区域筛选
export const mockSiteData: SiteInfo[] = [
  // 微电网研究院的站点
  {
    id: 'site_1',
    name: '微电网研究院总部',
    enterprise: '微电网研究院',
    region: '无锡市',
    address: '无锡市江阴市江苏科技城创新大道1号',
    type: 'substation',
  },
  {
    id: 'site_2',
    name: '微电网研究院实验基地',
    enterprise: '微电网研究院',
    region: '无锡市',
    address: '无锡市江阴市江苏科技城实验路88号',
    type: 'power_plant',
  },

  // 新能源科技公司的站点
  {
    id: 'site_3',
    name: '新能源发电站A',
    enterprise: '新能源科技公司',
    region: '朝阳区',
    address: '北京市朝阳区科技园区新能源大厦',
    type: 'power_plant',
  },
  {
    id: 'site_4',
    name: '新能源配电中心',
    enterprise: '新能源科技公司',
    region: '朝阳区',
    address: '北京市朝阳区望京街道配电中心',
    type: 'distribution_room',
  },
  {
    id: 'site_5',
    name: '新能源充电站',
    enterprise: '新能源科技公司',
    region: '海淀区',
    address: '北京市海淀区中关村软件园充电站',
    type: 'charging_station',
  },

  // 智慧电力集团的站点
  {
    id: 'site_6',
    name: '智慧配电中心',
    enterprise: '智慧电力集团',
    region: '石家庄市',
    address: '河北省石家庄市高新区智慧电力大厦',
    type: 'distribution_room',
  },
  {
    id: 'site_7',
    name: '智慧变电站',
    enterprise: '智慧电力集团',
    region: '唐山市',
    address: '河北省唐山市开平区智慧变电站',
    type: 'substation',
  },

  // 绿色能源有限公司的站点
  {
    id: 'site_8',
    name: '绿色充电站A',
    enterprise: '绿色能源有限公司',
    region: '石家庄市',
    address: '河北省石家庄市裕华区绿色能源园区',
    type: 'charging_station',
  },
  {
    id: 'site_9',
    name: '绿色发电厂',
    enterprise: '绿色能源有限公司',
    region: '唐山市',
    address: '河北省唐山市路南区绿色发电基地',
    type: 'power_plant',
  },
];
