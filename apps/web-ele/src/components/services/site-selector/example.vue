<script lang="ts" setup>
import type { SiteInfo } from './types';

import { ref } from 'vue';
import { Page } from '@vben/common-ui';
import { ElCard, ElMessage, ElButton } from 'element-plus';
import SiteSelector from './index.vue';

defineOptions({
  name: 'SiteSelectorExample',
});

// 选中的站点值
const selectedSiteValue = ref('');

// 站点选择器引用
const siteSelectorRef = ref<InstanceType<typeof SiteSelector>>();

// 站点选择事件处理
const handleSiteSelected = (site: SiteInfo) => {
  console.log('选中的站点:', site);
  ElMessage.success(`已选择站点: ${site.name}`);
};

// 获取当前选中站点
const getCurrentSite = () => {
  const site = siteSelectorRef.value?.getSelectedSite();
  if (site) {
    ElMessage.info(`当前选中站点: ${site.name} (${site.enterprise})`);
  } else {
    ElMessage.warning('未选择任何站点');
  }
};

// 清空选择
const clearSelection = () => {
  siteSelectorRef.value?.clear();
  ElMessage.info('已清空选择');
};

// 程序化打开选择器
const openSelector = () => {
  siteSelectorRef.value?.open();
};
</script>

<template>
  <Page
    description="基于VbenModal封装的站点选择组件，支持企业和区域分类选择"
    title="站点选择器示例"
  >
    <div class="space-y-6">
      <!-- 基础使用 -->
      <ElCard>
        <template #header>
          <div class="flex items-center justify-between">
            <span class="text-lg font-medium">基础使用</span>
          </div>
        </template>

        <div class="space-y-4">
          <div class="w-[400px]">
            <label class="block text-sm font-medium mb-2">选择站点：</label>
            <SiteSelector
              ref="siteSelectorRef"
              v-model="selectedSiteValue"
              placeholder="请点击选择站点"
              @site-selected="handleSiteSelected"
            />
          </div>

          <div class="text-sm text-gray-600">
            当前选中值: {{ selectedSiteValue || '未选择' }}
          </div>
        </div>
      </ElCard>

      <!-- 操作按钮 -->
      <ElCard>
        <template #header>
          <span class="text-lg font-medium">组件方法</span>
        </template>

        <div class="flex gap-3">
          <ElButton type="primary" @click="getCurrentSite">
            获取当前选中站点
          </ElButton>
          <ElButton type="warning" @click="clearSelection">
            清空选择
          </ElButton>
          <ElButton type="info" @click="openSelector">
            程序化打开选择器
          </ElButton>
        </div>
      </ElCard>

      <!-- 功能说明 -->
      <ElCard>
        <template #header>
          <span class="text-lg font-medium">功能说明</span>
        </template>

        <div class="space-y-3 text-sm">
          <div>
            <strong>左侧功能：</strong>
            <ul class="list-disc list-inside ml-4 mt-1 space-y-1">
              <li>企业标签页：显示企业树形结构，支持搜索和单选</li>
              <li>区域标签页：显示区域树形结构（省-市层级），支持搜索和单选</li>
              <li>选中树节点会自动过滤右侧表格数据</li>
            </ul>
          </div>

          <div>
            <strong>右侧功能：</strong>
            <ul class="list-disc list-inside ml-4 mt-1 space-y-1">
              <li>VXE表格显示站点列表（站点名称、企业、区域、用户地址）</li>
              <li>搜索表单：站点类型下拉选择 + 搜索关键字输入</li>
              <li>支持分页、排序、行选择等功能</li>
              <li>点击表格行选中站点，确定按钮提交选择</li>
            </ul>
          </div>

          <div>
            <strong>交互方式：</strong>
            <ul class="list-disc list-inside ml-4 mt-1 space-y-1">
              <li>点击输入框或聚焦输入框触发Modal显示</li>
              <li>支持清空按钮快速清除选择</li>
              <li>Modal内确定/取消按钮控制选择流程</li>
            </ul>
          </div>
        </div>
      </ElCard>
    </div>
  </Page>
</template>

<style scoped>
:deep(.el-card__header) {
  background-color: hsl(var(--muted));
}
</style>
