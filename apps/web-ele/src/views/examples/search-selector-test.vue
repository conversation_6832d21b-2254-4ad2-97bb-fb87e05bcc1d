<script setup lang="ts">
import { ref } from 'vue';
import { Page } from '@vben/common-ui';
import { useVbenForm } from '#/adapter/form';
import { searchEnterprisesApi } from '#/api/energy/enterprise';

// 表单数据
const formData = ref({
  enterprise: undefined,
});

// 创建表单
const [TestForm] = useVbenForm({
  // 默认展开
  collapsed: false,
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  // 提交函数
  handleSubmit: (values) => {
    console.log('Form submitted:', values);
  },
  // 水平布局，label和input在同一行
  layout: 'horizontal',
  schema: [
    {
      component: 'SearchSelector',
      fieldName: 'enterprise',
      label: '企业选择',
      componentProps: {
        class: 'w-full sm:max-w-xs md:max-w-sm lg:max-w-md xl:max-w-sm',
        placeholder: '请选择企业',
        searchPlaceholder: '搜索企业名称',
        buttonType: 'default',
        buttonSize: 'default',
        placement: 'bottom-start',
        popoverWidth: 300,
        filterable: true,
        remote: false, // 设置为 false，这样组件会在挂载时自动加载数据
        // 使用异步数据源获取企业列表
        dataSource: async (keyword?: string) => {
          try {
            console.log('SearchSelector dataSource called with keyword:', keyword);
            // 调用企业搜索API，当 keyword 为空或 undefined 时，获取所有企业列表
            const result = await searchEnterprisesApi(keyword || undefined);
            console.log('Enterprise list loaded:', result.length, 'items');
            return result;
          } catch (error) {
            console.error('获取企业列表失败:', error);
            // 如果API调用失败，返回模拟数据作为降级方案
            const mockData = [
              { label: '北京新能源科技有限公司', value: 'enterprise_001' },
              { label: '上海绿色电力集团', value: 'enterprise_002' },
              { label: '深圳智慧能源股份公司', value: 'enterprise_003' },
              { label: '广州清洁能源发展公司', value: 'enterprise_004' },
              { label: '杭州可再生能源企业', value: 'enterprise_005' },
            ];

            // 如果有搜索关键词，进行过滤
            if (keyword && keyword.trim()) {
              return mockData.filter((item) =>
                item.label.toLowerCase().includes(keyword.toLowerCase()),
              );
            }

            console.log('Using fallback enterprise data:', mockData.length, 'items');
            return mockData;
          }
        },
        // 监听企业选择变化
        onChange: (value: number | string, option: any) => {
          console.log('Enterprise selected:', value, option);
          formData.value.enterprise = value;
        },
      },
    },
    {
      component: 'Input',
      fieldName: 'testInput',
      label: '测试输入',
      componentProps: {
        placeholder: '请输入测试内容',
      },
    },
  ],
  // 大屏一行显示2个，中屏一行显示2个，小屏一行显示1个
  wrapperClass: 'grid-cols-1 md:grid-cols-2',
});
</script>

<template>
  <Page auto-content-height>
    <div class="p-4">
      <h2 class="text-2xl font-bold mb-4">SearchSelector 组件测试</h2>
      
      <div class="mb-6">
        <h3 class="text-lg font-semibold mb-2">当前表单数据：</h3>
        <pre class="bg-gray-100 p-3 rounded text-sm">{{ JSON.stringify(formData, null, 2) }}</pre>
      </div>

      <div class="border rounded-lg p-4">
        <h3 class="text-lg font-semibold mb-4">表单测试</h3>
        <TestForm />
      </div>
    </div>
  </Page>
</template>

<style scoped>
.page-container {
  @apply h-full;
}
</style>
